package com.pugwoo.rainbow.webdata.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * 抓取互联网数据的配置
 * <AUTHOR>
 */
@Data
@Table("t_web_data_conf")
public class WebDataConfigDO extends AdminCoreDO {

	// 抓取标识code
	@Column("code")
	private String code;
	
    // 名称
    @Column(value = "name")
    private String name;
	
	// 抓取url，变量为${i}
	@Column("url")
	private String url;
	
	// url传递参数，json格式
	@Column("param")
	private String param;
	
	// url获取方式，默认get
	@Column("method")
	private String method;
	
	// 变量开始值，含
	@Column("i_start")
	@JsonProperty("iStart") // 这里我应该是发现了jackson的一个bug了，iStart转成json后变成了istart
	private Integer iStart;
	
	// 变量结束值，含
	@Column("i_end")
	@JsonProperty("iEnd")
	private Integer iEnd;
	
	// 自定义序号生成类
	@Column("custom_seq_classname")
	private String customSeqClassname;
	
	// json字符串，自定义序号配置
	@Column("custom_seq_conf")
	private String customSeqConf;
	
	@Column("block_regex")
	private String blockRegex;
	
	// 结果分析regex，每行一个，name=regex_express
	@Column("parse_regex")
	private String parseRegex;
	
	// 保存为本地文件的名称
	@Column("file_save")
	private String fileSave;
	
	// 处理抓取内容的类全名
	@Column("handler_classname")
	private String handlerClassname;
	
	// 线程数
	@Column("threads")
	private Integer threads;
	
    // 网站编码
    @Column(value = "encode")
    private String encode;

	/** 是否启用cron定时任务，默认启用<br/>Column: [enable_cron] */
	@Column(value = "enable_cron")
	private Boolean enableCron;

	/** 定时任务表达式<br/>Column: [cron_expression] */
	@Column(value = "cron_expression")
	private String cronExpression;

	/** 每次请求之后睡眠多少毫秒<br/>Column: [sleep_ms_each] */
	@Column(value = "sleep_ms_each")
	private Integer sleepMsEach;

	/** http请求失败时重试次数，这里特指非200错误码，而不是内容不符合预期时的重试次数<br/>Column: [http_retry_times] */
	@Column(value = "http_retry_times")
	private Integer httpRetryTimes;

	/** 失败告警阈值，大于等于该次数时告警<br/>Column: [fail_notice_valve] */
	@Column(value = "fail_notice_valve")
	private Integer failNoticeValve;

	/** 告警邮箱，多个分号隔开<br/>Column: [send_email] */
	@Column(value = "send_email")
	private Integer sendEmail;

}
